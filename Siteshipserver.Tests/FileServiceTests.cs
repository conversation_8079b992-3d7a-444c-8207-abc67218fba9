using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Siteshipserver.Data;
using Siteshipserver.DTOs;
using Siteshipserver.Models;
using Siteshipserver.Services;
using Xunit;

namespace Siteshipserver.Tests;

public class FileServiceTests
{
    private IServiceProvider CreateServiceProvider()
    {
        var services = new ServiceCollection();

        services.AddDbContext<ApplicationDbContext>(options =>
            options.UseInMemoryDatabase($"TestFile_{Guid.NewGuid()}"));

        services.AddLogging();
        services.AddScoped<IDockerService, MockDockerService>();
        services.AddScoped<IFileService, FileService>();

        return services.BuildServiceProvider();
    }

    private async Task<(User user, Project project)> CreateTestUserAndProject(ApplicationDbContext context)
    {
        var user = new User
        {
            Username = "testuser",
            Email = "<EMAIL>",
            PasswordHash = "hashedpassword",
            FirstName = "Test",
            LastName = "User"
        };

        context.Users.Add(user);
        await context.SaveChangesAsync();

        var project = new Project
        {
            Name = "Test Project",
            Description = "A test project",
            Type = ProjectType.Static,
            UserId = user.Id,
            ContainerStatus = ContainerStatus.Stopped,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        context.Projects.Add(project);
        await context.SaveChangesAsync();

        return (user, project);
    }

    [Fact]
    public async Task CreateFile_WithValidData_ReturnsFile()
    {
        // Arrange
        var serviceProvider = CreateServiceProvider();
        using var scope = serviceProvider.CreateScope();
        var fileService = scope.ServiceProvider.GetRequiredService<IFileService>();
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        context.Database.EnsureCreated();

        var (user, project) = await CreateTestUserAndProject(context);

        var createFileDto = new CreateFileDto
        {
            FileName = "index.html",
            FilePath = "/",
            Content = "<html><body>Hello World</body></html>"
        };

        // Act
        var result = await fileService.CreateFileAsync(project.Id, user.Id, createFileDto);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("index.html", result.FileName);
        Assert.Equal("/", result.FilePath);
        Assert.Equal("<html><body>Hello World</body></html>", result.Content);
        Assert.Equal("text/html", result.MimeType);

        // Verify file was saved to database
        var savedFile = await context.ProjectFiles.FirstOrDefaultAsync(f => f.FileName == "index.html");
        Assert.NotNull(savedFile);
        Assert.Equal(project.Id, savedFile.ProjectId);
    }

    [Fact]
    public async Task GetProjectFiles_ReturnsProjectFiles()
    {
        // Arrange
        var serviceProvider = CreateServiceProvider();
        using var scope = serviceProvider.CreateScope();
        var fileService = scope.ServiceProvider.GetRequiredService<IFileService>();
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        context.Database.EnsureCreated();

        var (user, project) = await CreateTestUserAndProject(context);

        // Create test files
        var file1 = new ProjectFile
        {
            FileName = "index.html",
            FilePath = "/",
            Content = "<html><body>Hello</body></html>",
            MimeType = "text/html",
            FileSize = 100,
            ProjectId = project.Id
        };

        var file2 = new ProjectFile
        {
            FileName = "style.css",
            FilePath = "/css/",
            Content = "body { color: red; }",
            MimeType = "text/css",
            FileSize = 50,
            ProjectId = project.Id
        };

        context.ProjectFiles.AddRange(file1, file2);
        await context.SaveChangesAsync();

        // Act
        var result = await fileService.GetProjectFilesAsync(project.Id, user.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.Contains(result, f => f.FileName == "index.html");
        Assert.Contains(result, f => f.FileName == "style.css");
    }

    [Fact]
    public async Task GetFileById_WithValidId_ReturnsFile()
    {
        // Arrange
        var serviceProvider = CreateServiceProvider();
        using var scope = serviceProvider.CreateScope();
        var fileService = scope.ServiceProvider.GetRequiredService<IFileService>();
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        context.Database.EnsureCreated();

        var (user, project) = await CreateTestUserAndProject(context);

        var file = new ProjectFile
        {
            FileName = "test.html",
            FilePath = "/",
            Content = "<html><body>Test</body></html>",
            MimeType = "text/html",
            FileSize = 100,
            ProjectId = project.Id
        };

        context.ProjectFiles.Add(file);
        await context.SaveChangesAsync();

        // Act
        var result = await fileService.GetFileByIdAsync(file.Id, user.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("test.html", result.FileName);
        Assert.Equal("<html><body>Test</body></html>", result.Content);
    }

    [Fact]
    public async Task UpdateFile_WithValidData_ReturnsUpdatedFile()
    {
        // Arrange
        var serviceProvider = CreateServiceProvider();
        using var scope = serviceProvider.CreateScope();
        var fileService = scope.ServiceProvider.GetRequiredService<IFileService>();
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        context.Database.EnsureCreated();

        var (user, project) = await CreateTestUserAndProject(context);

        var file = new ProjectFile
        {
            FileName = "original.html",
            FilePath = "/",
            Content = "<html><body>Original</body></html>",
            MimeType = "text/html",
            FileSize = 100,
            ProjectId = project.Id
        };

        context.ProjectFiles.Add(file);
        await context.SaveChangesAsync();

        var updateDto = new UpdateFileDto
        {
            FileName = "updated.html",
            Content = "<html><body>Updated</body></html>"
        };

        // Act
        var result = await fileService.UpdateFileAsync(file.Id, user.Id, updateDto);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("updated.html", result.FileName);
        Assert.Equal("<html><body>Updated</body></html>", result.Content);

        // Verify changes were saved
        var updatedFile = await context.ProjectFiles.FindAsync(file.Id);
        Assert.Equal("updated.html", updatedFile!.FileName);
        Assert.Equal("<html><body>Updated</body></html>", updatedFile.Content);
    }

    [Fact]
    public async Task DeleteFile_WithValidId_ReturnsTrue()
    {
        // Arrange
        var serviceProvider = CreateServiceProvider();
        using var scope = serviceProvider.CreateScope();
        var fileService = scope.ServiceProvider.GetRequiredService<IFileService>();
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        context.Database.EnsureCreated();

        var (user, project) = await CreateTestUserAndProject(context);

        var file = new ProjectFile
        {
            FileName = "delete-me.html",
            FilePath = "/",
            Content = "<html><body>Delete me</body></html>",
            MimeType = "text/html",
            FileSize = 100,
            ProjectId = project.Id
        };

        context.ProjectFiles.Add(file);
        await context.SaveChangesAsync();

        // Act
        var result = await fileService.DeleteFileAsync(file.Id, user.Id);

        // Assert
        Assert.True(result);

        // Verify file was marked as deleted (soft delete)
        var deletedFile = await context.ProjectFiles.FindAsync(file.Id);
        Assert.True(deletedFile!.IsDeleted);
    }

    [Fact]
    public async Task GetFileById_WithWrongUserId_ThrowsException()
    {
        // Arrange
        var serviceProvider = CreateServiceProvider();
        using var scope = serviceProvider.CreateScope();
        var fileService = scope.ServiceProvider.GetRequiredService<IFileService>();
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        context.Database.EnsureCreated();

        var (user1, project) = await CreateTestUserAndProject(context);

        var user2 = new User
        {
            Username = "otheruser",
            Email = "<EMAIL>",
            PasswordHash = "hashedpassword",
            FirstName = "Other",
            LastName = "User"
        };
        context.Users.Add(user2);
        await context.SaveChangesAsync();

        var file = new ProjectFile
        {
            FileName = "private.html",
            FilePath = "/",
            Content = "<html><body>Private</body></html>",
            MimeType = "text/html",
            FileSize = 100,
            ProjectId = project.Id
        };

        context.ProjectFiles.Add(file);
        await context.SaveChangesAsync();

        // Act & Assert - Try to access user1's file as user2
        var exception = await Assert.ThrowsAsync<KeyNotFoundException>(
            () => fileService.GetFileByIdAsync(file.Id, user2.Id));
        Assert.Contains("access denied", exception.Message);
    }

    [Fact]
    public async Task CreateFile_WithDuplicatePath_ThrowsException()
    {
        // Arrange
        var serviceProvider = CreateServiceProvider();
        using var scope = serviceProvider.CreateScope();
        var fileService = scope.ServiceProvider.GetRequiredService<IFileService>();
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        context.Database.EnsureCreated();

        var (user, project) = await CreateTestUserAndProject(context);

        // Create first file
        var existingFile = new ProjectFile
        {
            FileName = "index.html",
            FilePath = "/",
            Content = "<html><body>Existing</body></html>",
            MimeType = "text/html",
            FileSize = 100,
            ProjectId = project.Id
        };

        context.ProjectFiles.Add(existingFile);
        await context.SaveChangesAsync();

        var createFileDto = new CreateFileDto
        {
            FileName = "index.html",
            FilePath = "/", // Same path as existing file
            Content = "<html><body>Duplicate</body></html>"
        };

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(
            () => fileService.CreateFileAsync(project.Id, user.Id, createFileDto));
        Assert.Contains("already exists", exception.Message);
    }
}
