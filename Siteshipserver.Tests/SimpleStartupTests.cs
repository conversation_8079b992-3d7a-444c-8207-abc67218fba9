using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.TestHost;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using System.Net;
using Xunit;

namespace Siteshipserver.Tests;

public class SimpleStartupTests
{
    [Fact]
    public async Task CanCreateMinimalWebApplication()
    {
        // Arrange
        var builder = WebApplication.CreateBuilder();
        
        // Configure minimal services
        builder.Services.AddControllers();
        
        var app = builder.Build();
        
        // Configure minimal pipeline
        app.MapGet("/health", () => new { status = "healthy", timestamp = DateTime.UtcNow });
        app.MapControllers();
        
        // Act & Assert
        using var host = new TestServer(new WebHostBuilder()
            .UseStartup<TestStartup>());
        
        var client = host.CreateClient();
        var response = await client.GetAsync("/health");
        
        Assert.Equal(HttpStatusCode.OK, response.StatusCode);
    }
}

public class TestStartup
{
    public void ConfigureServices(IServiceCollection services)
    {
        services.AddControllers();
    }

    public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
    {
        app.UseRouting();
        app.UseEndpoints(endpoints =>
        {
            endpoints.MapGet("/health", async context =>
            {
                await context.Response.WriteAsync("{\"status\":\"healthy\"}");
            });
        });
    }
}
