using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Siteshipserver.Data;
using Xunit;

namespace Siteshipserver.Tests;

public class DatabaseConnectivityTests
{
    [Fact]
    public void CanCreateInMemoryDatabase()
    {
        // Arrange
        var services = new ServiceCollection();
        services.AddDbContext<ApplicationDbContext>(options =>
            options.UseInMemoryDatabase("TestDatabase"));
        services.AddLogging();

        var serviceProvider = services.BuildServiceProvider();

        // Act
        using var scope = serviceProvider.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

        // Assert
        Assert.NotNull(context);
        Assert.True(context.Database.CanConnect());
    }

    [Fact]
    public void CanCreateDatabaseSchema()
    {
        // Arrange
        var services = new ServiceCollection();
        services.AddDbContext<ApplicationDbContext>(options =>
            options.UseInMemoryDatabase("TestDatabaseSchema"));
        services.AddLogging();

        var serviceProvider = services.BuildServiceProvider();

        // Act
        using var scope = serviceProvider.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        context.Database.EnsureCreated();

        // Assert
        Assert.True(context.Database.CanConnect());
        
        // Verify tables exist by checking if we can query them
        var userCount = context.Users.Count();
        var projectCount = context.Projects.Count();
        var fileCount = context.ProjectFiles.Count();
        
        Assert.True(userCount >= 0);
        Assert.True(projectCount >= 0);
        Assert.True(fileCount >= 0);
    }

    [Fact]
    public void CanAddAndRetrieveUser()
    {
        // Arrange
        var services = new ServiceCollection();
        services.AddDbContext<ApplicationDbContext>(options =>
            options.UseInMemoryDatabase("TestUserOperations"));
        services.AddLogging();

        var serviceProvider = services.BuildServiceProvider();

        // Act
        using var scope = serviceProvider.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        context.Database.EnsureCreated();

        var user = new Siteshipserver.Models.User
        {
            Username = "testuser",
            Email = "<EMAIL>",
            PasswordHash = "hashedpassword",
            FirstName = "Test",
            LastName = "User"
        };

        context.Users.Add(user);
        context.SaveChanges();

        // Assert
        var retrievedUser = context.Users.FirstOrDefault(u => u.Username == "testuser");
        Assert.NotNull(retrievedUser);
        Assert.Equal("<EMAIL>", retrievedUser.Email);
        Assert.Equal("Test", retrievedUser.FirstName);
    }
}
