using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Siteshipserver.Data;
using Siteshipserver.DTOs;
using Siteshipserver.Services;
using Xunit;

namespace Siteshipserver.Tests;

public class AuthServiceTests
{
    private IServiceProvider CreateServiceProvider()
    {
        var services = new ServiceCollection();

        // Add in-memory database
        services.AddDbContext<ApplicationDbContext>(options =>
            options.UseInMemoryDatabase($"TestAuth_{Guid.NewGuid()}"));

        // Add configuration
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["JwtSettings:SecretKey"] = "test-jwt-secret-key-for-testing-minimum-32-characters-long",
                ["JwtSettings:Issuer"] = "TestIssuer",
                ["JwtSettings:Audience"] = "TestAudience",
                ["JwtSettings:ExpirationHours"] = "1"
            })
            .Build();

        services.AddSingleton<IConfiguration>(configuration);
        services.AddLogging();
        services.AddScoped<IAuthService, AuthService>();

        return services.BuildServiceProvider();
    }

    [Fact]
    public async Task RegisterUser_WithValidData_ReturnsSuccess()
    {
        // Arrange
        var serviceProvider = CreateServiceProvider();
        using var scope = serviceProvider.CreateScope();
        var authService = scope.ServiceProvider.GetRequiredService<IAuthService>();
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        context.Database.EnsureCreated();

        var registerDto = new RegisterDto
        {
            Username = "testuser",
            Email = "<EMAIL>",
            Password = "password123",
            FirstName = "Test",
            LastName = "User"
        };

        // Act
        var result = await authService.RegisterAsync(registerDto);

        // Assert
        Assert.NotNull(result);
        Assert.NotEmpty(result.AccessToken);
        Assert.NotNull(result.User);
        Assert.Equal("testuser", result.User.Username);

        // Verify user was created in database
        var user = await context.Users.FirstOrDefaultAsync(u => u.Username == "testuser");
        Assert.NotNull(user);
        Assert.Equal("<EMAIL>", user.Email);
    }

    [Fact]
    public async Task RegisterUser_WithDuplicateEmail_ThrowsException()
    {
        // Arrange
        var serviceProvider = CreateServiceProvider();
        using var scope = serviceProvider.CreateScope();
        var authService = scope.ServiceProvider.GetRequiredService<IAuthService>();
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        context.Database.EnsureCreated();

        var registerDto1 = new RegisterDto
        {
            Username = "testuser1",
            Email = "<EMAIL>",
            Password = "password123",
            FirstName = "Test",
            LastName = "User"
        };

        var registerDto2 = new RegisterDto
        {
            Username = "testuser2",
            Email = "<EMAIL>", // Same email
            Password = "password123",
            FirstName = "Test",
            LastName = "User"
        };

        // Act
        var result1 = await authService.RegisterAsync(registerDto1);

        // Assert
        Assert.NotNull(result1);
        Assert.NotEmpty(result1.AccessToken);

        // Second registration should throw exception
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(
            () => authService.RegisterAsync(registerDto2));
        Assert.Contains("already exists", exception.Message);
    }

    [Fact]
    public async Task LoginUser_WithValidCredentials_ReturnsToken()
    {
        // Arrange
        var serviceProvider = CreateServiceProvider();
        using var scope = serviceProvider.CreateScope();
        var authService = scope.ServiceProvider.GetRequiredService<IAuthService>();
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        context.Database.EnsureCreated();

        // First register a user
        var registerDto = new RegisterDto
        {
            Username = "testuser",
            Email = "<EMAIL>",
            Password = "password123",
            FirstName = "Test",
            LastName = "User"
        };
        await authService.RegisterAsync(registerDto);

        var loginDto = new LoginDto
        {
            EmailOrUsername = "<EMAIL>",
            Password = "password123"
        };

        // Act
        var result = await authService.LoginAsync(loginDto);

        // Assert
        Assert.NotNull(result);
        Assert.NotEmpty(result.AccessToken);
        Assert.NotNull(result.User);
        Assert.Equal("testuser", result.User.Username);
    }

    [Fact]
    public async Task LoginUser_WithInvalidCredentials_ThrowsException()
    {
        // Arrange
        var serviceProvider = CreateServiceProvider();
        using var scope = serviceProvider.CreateScope();
        var authService = scope.ServiceProvider.GetRequiredService<IAuthService>();
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        context.Database.EnsureCreated();

        var loginDto = new LoginDto
        {
            EmailOrUsername = "<EMAIL>",
            Password = "wrongpassword"
        };

        // Act & Assert
        var exception = await Assert.ThrowsAsync<UnauthorizedAccessException>(
            () => authService.LoginAsync(loginDto));
        Assert.Contains("Invalid", exception.Message);
    }

    [Fact]
    public async Task ValidateToken_WithValidToken_ReturnsTrue()
    {
        // Arrange
        var serviceProvider = CreateServiceProvider();
        using var scope = serviceProvider.CreateScope();
        var authService = scope.ServiceProvider.GetRequiredService<IAuthService>();
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        context.Database.EnsureCreated();

        // Register and login to get a token
        var registerDto = new RegisterDto
        {
            Username = "testuser",
            Email = "<EMAIL>",
            Password = "password123",
            FirstName = "Test",
            LastName = "User"
        };
        await authService.RegisterAsync(registerDto);

        var loginDto = new LoginDto
        {
            EmailOrUsername = "<EMAIL>",
            Password = "password123"
        };
        var loginResult = await authService.LoginAsync(loginDto);

        // Act
        var isValid = await authService.ValidateTokenAsync(loginResult.AccessToken);

        // Assert
        Assert.True(isValid);
    }

    [Fact]
    public async Task ValidateToken_WithInvalidToken_ReturnsFalse()
    {
        // Arrange
        var serviceProvider = CreateServiceProvider();
        using var scope = serviceProvider.CreateScope();
        var authService = scope.ServiceProvider.GetRequiredService<IAuthService>();

        // Act
        var isValid = await authService.ValidateTokenAsync("invalid.token.here");

        // Assert
        Assert.False(isValid);
    }
}
