using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Siteshipserver.Data;
using Siteshipserver.DTOs;
using Siteshipserver.Models;
using Siteshipserver.Services;
using Xunit;

namespace Siteshipserver.Tests;

public class ProjectServiceTests
{
    private IServiceProvider CreateServiceProvider()
    {
        var services = new ServiceCollection();

        services.AddDbContext<ApplicationDbContext>(options =>
            options.UseInMemoryDatabase($"TestProject_{Guid.NewGuid()}"));

        services.AddLogging();
        services.AddScoped<IDockerService, MockDockerService>();
        services.AddScoped<IProjectService, ProjectService>();

        return services.BuildServiceProvider();
    }

    private async Task<User> CreateTestUser(ApplicationDbContext context)
    {
        var user = new User
        {
            Username = "testuser",
            Email = "<EMAIL>",
            PasswordHash = "hashedpassword",
            FirstName = "Test",
            LastName = "User"
        };

        context.Users.Add(user);
        await context.SaveChangesAsync();
        return user;
    }

    [Fact]
    public async Task CreateProject_WithValidData_ReturnsProject()
    {
        // Arrange
        var serviceProvider = CreateServiceProvider();
        using var scope = serviceProvider.CreateScope();
        var projectService = scope.ServiceProvider.GetRequiredService<IProjectService>();
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        context.Database.EnsureCreated();

        var user = await CreateTestUser(context);

        var createProjectDto = new CreateProjectDto
        {
            Name = "Test Project",
            Description = "A test project",
            Type = Siteshipserver.Models.ProjectType.Static,
            IsPublic = false
        };

        // Act
        var result = await projectService.CreateProjectAsync(user.Id, createProjectDto);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("Test Project", result.Name);
        Assert.Equal("A test project", result.Description);
        Assert.Equal(user.Id, result.UserId);

        // Verify project was saved to database
        var savedProject = await context.Projects.FirstOrDefaultAsync(p => p.Name == "Test Project");
        Assert.NotNull(savedProject);
    }

    [Fact]
    public async Task GetUserProjects_ReturnsUserProjects()
    {
        // Arrange
        var serviceProvider = CreateServiceProvider();
        using var scope = serviceProvider.CreateScope();
        var projectService = scope.ServiceProvider.GetRequiredService<IProjectService>();
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        context.Database.EnsureCreated();

        var user = await CreateTestUser(context);

        // Create test projects
        var project1 = new Project
        {
            Name = "Project 1",
            Description = "First project",
            UserId = user.Id
        };

        var project2 = new Project
        {
            Name = "Project 2",
            Description = "Second project",
            UserId = user.Id
        };

        context.Projects.AddRange(project1, project2);
        await context.SaveChangesAsync();

        // Act
        var result = await projectService.GetUserProjectsAsync(user.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count());
        Assert.Contains(result, p => p.Name == "Project 1");
        Assert.Contains(result, p => p.Name == "Project 2");
    }

    [Fact]
    public async Task GetProjectById_WithValidId_ReturnsProject()
    {
        // Arrange
        var serviceProvider = CreateServiceProvider();
        using var scope = serviceProvider.CreateScope();
        var projectService = scope.ServiceProvider.GetRequiredService<IProjectService>();
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        context.Database.EnsureCreated();

        var user = await CreateTestUser(context);

        var project = new Project
        {
            Name = "Test Project",
            Description = "A test project",
            UserId = user.Id
        };

        context.Projects.Add(project);
        await context.SaveChangesAsync();

        // Act
        var result = await projectService.GetProjectByIdAsync(project.Id, user.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("Test Project", result.Name);
        Assert.Equal("A test project", result.Description);
    }

    [Fact]
    public async Task GetProjectById_WithInvalidId_ThrowsException()
    {
        // Arrange
        var serviceProvider = CreateServiceProvider();
        using var scope = serviceProvider.CreateScope();
        var projectService = scope.ServiceProvider.GetRequiredService<IProjectService>();
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        context.Database.EnsureCreated();

        var user = await CreateTestUser(context);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<KeyNotFoundException>(
            () => projectService.GetProjectByIdAsync(999, user.Id));
        Assert.Contains("Project not found", exception.Message);
    }

    [Fact]
    public async Task UpdateProject_WithValidData_ReturnsUpdatedProject()
    {
        // Arrange
        var serviceProvider = CreateServiceProvider();
        using var scope = serviceProvider.CreateScope();
        var projectService = scope.ServiceProvider.GetRequiredService<IProjectService>();
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        context.Database.EnsureCreated();

        var user = await CreateTestUser(context);

        var project = new Project
        {
            Name = "Original Name",
            Description = "Original Description",
            UserId = user.Id
        };

        context.Projects.Add(project);
        await context.SaveChangesAsync();

        var updateDto = new UpdateProjectDto
        {
            Name = "Updated Name",
            Description = "Updated Description"
        };

        // Act
        var result = await projectService.UpdateProjectAsync(project.Id, user.Id, updateDto);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("Updated Name", result.Name);
        Assert.Equal("Updated Description", result.Description);

        // Verify changes were saved
        var updatedProject = await context.Projects.FindAsync(project.Id);
        Assert.Equal("Updated Name", updatedProject!.Name);
        Assert.Equal("Updated Description", updatedProject.Description);
    }

    [Fact]
    public async Task DeleteProject_WithValidId_ReturnsTrue()
    {
        // Arrange
        var serviceProvider = CreateServiceProvider();
        using var scope = serviceProvider.CreateScope();
        var projectService = scope.ServiceProvider.GetRequiredService<IProjectService>();
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        context.Database.EnsureCreated();

        var user = await CreateTestUser(context);

        var project = new Project
        {
            Name = "Project to Delete",
            Description = "This project will be deleted",
            UserId = user.Id
        };

        context.Projects.Add(project);
        await context.SaveChangesAsync();

        // Act
        var result = await projectService.DeleteProjectAsync(project.Id, user.Id);

        // Assert
        Assert.True(result);

        // Verify project was deleted
        var deletedProject = await context.Projects.FindAsync(project.Id);
        Assert.Null(deletedProject);
    }

    [Fact]
    public async Task DeleteProject_WithInvalidId_ThrowsException()
    {
        // Arrange
        var serviceProvider = CreateServiceProvider();
        using var scope = serviceProvider.CreateScope();
        var projectService = scope.ServiceProvider.GetRequiredService<IProjectService>();
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        context.Database.EnsureCreated();

        var user = await CreateTestUser(context);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<KeyNotFoundException>(
            () => projectService.DeleteProjectAsync(999, user.Id));
        Assert.Contains("Project not found", exception.Message);
    }

    [Fact]
    public async Task GetProjectById_WithWrongUserId_ThrowsException()
    {
        // Arrange
        var serviceProvider = CreateServiceProvider();
        using var scope = serviceProvider.CreateScope();
        var projectService = scope.ServiceProvider.GetRequiredService<IProjectService>();
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        context.Database.EnsureCreated();

        var user1 = await CreateTestUser(context);

        var user2 = new User
        {
            Username = "otheruser",
            Email = "<EMAIL>",
            PasswordHash = "hashedpassword",
            FirstName = "Other",
            LastName = "User"
        };
        context.Users.Add(user2);
        await context.SaveChangesAsync();

        var project = new Project
        {
            Name = "User1's Project",
            Description = "This belongs to user1",
            UserId = user1.Id,
            Type = Siteshipserver.Models.ProjectType.Static,
            ContainerStatus = Siteshipserver.Models.ContainerStatus.Stopped,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        context.Projects.Add(project);
        await context.SaveChangesAsync();

        // Act & Assert - Try to access user1's project as user2
        var exception = await Assert.ThrowsAsync<KeyNotFoundException>(
            () => projectService.GetProjectByIdAsync(project.Id, user2.Id));
        Assert.Contains("Project not found", exception.Message);
    }
}

// Mock Docker Service for testing
public class MockDockerService : IDockerService
{
    public Task<string> CreateContainerAsync(Project project)
    {
        return Task.FromResult($"mock-container-{project.Id}");
    }

    public Task<bool> StartContainerAsync(string containerId)
    {
        return Task.FromResult(true);
    }

    public Task<bool> StopContainerAsync(string containerId)
    {
        return Task.FromResult(true);
    }

    public Task<bool> RestartContainerAsync(string containerId)
    {
        return Task.FromResult(true);
    }

    public Task<bool> DeleteContainerAsync(string containerId)
    {
        return Task.FromResult(true);
    }

    public Task<ContainerStatus> GetContainerStatusAsync(string containerId)
    {
        return Task.FromResult(ContainerStatus.Running);
    }

    public Task<string?> GetContainerLogsAsync(string containerId, int lines = 100)
    {
        return Task.FromResult<string?>("Mock container logs");
    }

    public Task<bool> DeployProjectAsync(Project project)
    {
        return Task.FromResult(true);
    }

    public Task<string> GetContainerUrlAsync(string containerId, string? port = null)
    {
        return Task.FromResult($"http://localhost:3000");
    }

    public Task<bool> UpdateContainerFilesAsync(string containerId, Dictionary<string, string> files)
    {
        return Task.FromResult(true);
    }
}
