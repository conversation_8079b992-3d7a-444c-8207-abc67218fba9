using System.Diagnostics;
using System.Net;
using System.Text;
using System.Text.Json;
using Xunit;
using Xunit.Abstractions;

namespace Siteshipserver.Tests;

public class PerformanceTests : IClassFixture<SiteshipWebApplicationFactory>
{
    private readonly SiteshipWebApplicationFactory _factory;
    private readonly HttpClient _client;
    private readonly ITestOutputHelper _output;

    public PerformanceTests(SiteshipWebApplicationFactory factory, ITestOutputHelper output)
    {
        _factory = factory;
        _client = _factory.CreateClient();
        _output = output;
    }

    [Fact]
    public async Task HealthCheck_PerformanceTest()
    {
        // Arrange
        const int requestCount = 100;
        var stopwatch = Stopwatch.StartNew();
        var tasks = new List<Task<HttpResponseMessage>>();

        // Act
        for (int i = 0; i < requestCount; i++)
        {
            tasks.Add(_client.GetAsync("/health"));
        }

        var responses = await Task.WhenAll(tasks);
        stopwatch.Stop();

        // Assert
        Assert.All(responses, response => response.EnsureSuccessStatusCode());

        var averageResponseTime = stopwatch.ElapsedMilliseconds / (double)requestCount;
        _output.WriteLine($"Health check - Average response time: {averageResponseTime:F2}ms for {requestCount} requests");
        _output.WriteLine($"Total time: {stopwatch.ElapsedMilliseconds}ms");

        // Performance assertion - health check should be fast
        Assert.True(averageResponseTime < 100, $"Average response time {averageResponseTime:F2}ms exceeds 100ms threshold");

        // Cleanup
        foreach (var response in responses)
        {
            response.Dispose();
        }
    }

    [Fact]
    public async Task ApiInfo_PerformanceTest()
    {
        // Arrange
        const int requestCount = 50;
        var stopwatch = Stopwatch.StartNew();
        var tasks = new List<Task<HttpResponseMessage>>();

        // Act
        for (int i = 0; i < requestCount; i++)
        {
            tasks.Add(_client.GetAsync("/api/info"));
        }

        var responses = await Task.WhenAll(tasks);
        stopwatch.Stop();

        // Assert
        Assert.All(responses, response => response.EnsureSuccessStatusCode());

        var averageResponseTime = stopwatch.ElapsedMilliseconds / (double)requestCount;
        _output.WriteLine($"API info - Average response time: {averageResponseTime:F2}ms for {requestCount} requests");
        _output.WriteLine($"Total time: {stopwatch.ElapsedMilliseconds}ms");

        // Performance assertion - API info should be fast
        Assert.True(averageResponseTime < 200, $"Average response time {averageResponseTime:F2}ms exceeds 200ms threshold");

        // Cleanup
        foreach (var response in responses)
        {
            response.Dispose();
        }
    }

    [Fact]
    public async Task ConcurrentUnauthorizedRequests_PerformanceTest()
    {
        // Arrange
        const int requestCount = 50;
        var stopwatch = Stopwatch.StartNew();
        var tasks = new List<Task<HttpResponseMessage>>();

        // Act - Test concurrent unauthorized requests to different endpoints
        var endpoints = new[]
        {
            "/api/projects",
            "/api/auth/profile",
            "/api/dashboard/stats",
            "/api/dashboard/activity",
            "/api/scan/1"
        };

        for (int i = 0; i < requestCount; i++)
        {
            var endpoint = endpoints[i % endpoints.Length];
            tasks.Add(_client.GetAsync(endpoint));
        }

        var responses = await Task.WhenAll(tasks);
        stopwatch.Stop();

        // Assert
        Assert.All(responses, response =>
            Assert.Equal(HttpStatusCode.Unauthorized, response.StatusCode));

        var averageResponseTime = stopwatch.ElapsedMilliseconds / (double)requestCount;
        _output.WriteLine($"Unauthorized requests - Average response time: {averageResponseTime:F2}ms for {requestCount} requests");
        _output.WriteLine($"Total time: {stopwatch.ElapsedMilliseconds}ms");

        // Performance assertion - unauthorized responses should be fast
        Assert.True(averageResponseTime < 150, $"Average response time {averageResponseTime:F2}ms exceeds 150ms threshold");

        // Cleanup
        foreach (var response in responses)
        {
            response.Dispose();
        }
    }

    [Fact]
    public async Task InvalidRequests_PerformanceTest()
    {
        // Arrange
        const int requestCount = 30;
        var stopwatch = Stopwatch.StartNew();
        var tasks = new List<Task<HttpResponseMessage>>();

        // Act - Test concurrent invalid requests
        var invalidData = "{ \"invalid\": \"data\" }";
        var content = new StringContent(invalidData, Encoding.UTF8, "application/json");

        for (int i = 0; i < requestCount; i++)
        {
            // Alternate between different endpoints
            if (i % 2 == 0)
            {
                tasks.Add(_client.PostAsync("/api/auth/register",
                    new StringContent(invalidData, Encoding.UTF8, "application/json")));
            }
            else
            {
                tasks.Add(_client.PostAsync("/api/auth/login",
                    new StringContent(invalidData, Encoding.UTF8, "application/json")));
            }
        }

        var responses = await Task.WhenAll(tasks);
        stopwatch.Stop();

        // Assert
        Assert.All(responses, response =>
            Assert.Equal(HttpStatusCode.BadRequest, response.StatusCode));

        var averageResponseTime = stopwatch.ElapsedMilliseconds / (double)requestCount;
        _output.WriteLine($"Invalid requests - Average response time: {averageResponseTime:F2}ms for {requestCount} requests");
        _output.WriteLine($"Total time: {stopwatch.ElapsedMilliseconds}ms");

        // Performance assertion - bad request responses should be reasonably fast
        Assert.True(averageResponseTime < 300, $"Average response time {averageResponseTime:F2}ms exceeds 300ms threshold");

        // Cleanup
        foreach (var response in responses)
        {
            response.Dispose();
        }
    }

    [Fact]
    public async Task DockerHealthCheck_PerformanceTest()
    {
        // Arrange
        const int requestCount = 20;
        var stopwatch = Stopwatch.StartNew();
        var tasks = new List<Task<HttpResponseMessage>>();

        // Act
        for (int i = 0; i < requestCount; i++)
        {
            tasks.Add(_client.GetAsync("/api/health/docker"));
        }

        var responses = await Task.WhenAll(tasks);
        stopwatch.Stop();

        // Assert
        Assert.All(responses, response => response.EnsureSuccessStatusCode());

        var averageResponseTime = stopwatch.ElapsedMilliseconds / (double)requestCount;
        _output.WriteLine($"Docker health check - Average response time: {averageResponseTime:F2}ms for {requestCount} requests");
        _output.WriteLine($"Total time: {stopwatch.ElapsedMilliseconds}ms");

        // Performance assertion - Docker health check might be slower but should be reasonable
        Assert.True(averageResponseTime < 1000, $"Average response time {averageResponseTime:F2}ms exceeds 1000ms threshold");

        // Cleanup
        foreach (var response in responses)
        {
            response.Dispose();
        }
    }

    [Fact]
    public async Task MixedEndpoints_LoadTest()
    {
        // Arrange
        const int totalRequests = 100;
        var stopwatch = Stopwatch.StartNew();
        var tasks = new List<Task<HttpResponseMessage>>();

        // Act - Mix of different endpoint types to simulate real usage
        var endpoints = new string[]
        {
            "/health",
            "/api/info",
            "/api/projects", // Will return 401
            "/api/auth/profile", // Will return 401
            "/api/health/docker",
            "/api/nonexistent", // Will return 404
        };

        for (int i = 0; i < totalRequests; i++)
        {
            var endpoint = endpoints[i % endpoints.Length];
            tasks.Add(_client.GetAsync(endpoint));
        }

        var responses = await Task.WhenAll(tasks);
        stopwatch.Stop();

        // Assert
        var successCount = responses.Count(r => r.IsSuccessStatusCode);
        var unauthorizedCount = responses.Count(r => r.StatusCode == HttpStatusCode.Unauthorized);
        var notFoundCount = responses.Count(r => r.StatusCode == HttpStatusCode.NotFound);

        _output.WriteLine($"Mixed load test results:");
        _output.WriteLine($"  Total requests: {totalRequests}");
        _output.WriteLine($"  Successful: {successCount}");
        _output.WriteLine($"  Unauthorized: {unauthorizedCount}");
        _output.WriteLine($"  Not Found: {notFoundCount}");
        _output.WriteLine($"  Total time: {stopwatch.ElapsedMilliseconds}ms");
        _output.WriteLine($"  Average response time: {stopwatch.ElapsedMilliseconds / (double)totalRequests:F2}ms");
        _output.WriteLine($"  Requests per second: {totalRequests / (stopwatch.ElapsedMilliseconds / 1000.0):F2}");

        // Performance assertions
        var averageResponseTime = stopwatch.ElapsedMilliseconds / (double)totalRequests;
        Assert.True(averageResponseTime < 500, $"Average response time {averageResponseTime:F2}ms exceeds 500ms threshold");

        var requestsPerSecond = totalRequests / (stopwatch.ElapsedMilliseconds / 1000.0);
        Assert.True(requestsPerSecond > 10, $"Requests per second {requestsPerSecond:F2} is below 10 RPS threshold");

        // Cleanup
        foreach (var response in responses)
        {
            response.Dispose();
        }
    }

    [Fact]
    public async Task MemoryUsage_Test()
    {
        // Arrange
        var initialMemory = GC.GetTotalMemory(true);
        const int requestCount = 50;

        // Act - Make multiple requests to test memory usage
        for (int i = 0; i < requestCount; i++)
        {
            using var response = await _client.GetAsync("/health");
            response.EnsureSuccessStatusCode();
            await response.Content.ReadAsStringAsync();
        }

        // Force garbage collection and measure memory
        GC.Collect();
        GC.WaitForPendingFinalizers();
        GC.Collect();

        var finalMemory = GC.GetTotalMemory(false);
        var memoryIncrease = finalMemory - initialMemory;

        // Assert
        _output.WriteLine($"Memory usage test:");
        _output.WriteLine($"  Initial memory: {initialMemory / 1024.0 / 1024.0:F2} MB");
        _output.WriteLine($"  Final memory: {finalMemory / 1024.0 / 1024.0:F2} MB");
        _output.WriteLine($"  Memory increase: {memoryIncrease / 1024.0 / 1024.0:F2} MB");
        _output.WriteLine($"  Requests processed: {requestCount}");

        // Memory should not increase dramatically for simple requests
        var memoryIncreasePerRequest = memoryIncrease / (double)requestCount;
        _output.WriteLine($"  Memory increase per request: {memoryIncreasePerRequest / 1024.0:F2} KB");

        // This is a loose assertion - memory usage can vary
        Assert.True(memoryIncreasePerRequest < 100 * 1024, // Less than 100KB per request
            $"Memory increase per request {memoryIncreasePerRequest / 1024.0:F2} KB exceeds 100KB threshold");
    }
}
