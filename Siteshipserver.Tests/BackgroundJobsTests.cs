using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using Siteshipserver.Data;
using Siteshipserver.Models;
using Siteshipserver.Services;
using System.Text.Json;
using Xunit;
using Hangfire;

namespace Siteshipserver.Tests;

public class BackgroundJobsTests : IDisposable
{
    private readonly ApplicationDbContext _context;
    private readonly ScanService _scanService;
    private readonly Mock<IDockerService> _mockDockerService;
    private readonly Mock<ILogger<ScanService>> _mockLogger;
    private readonly Mock<IBackgroundJobClient> _mockBackgroundJobClient;

    public BackgroundJobsTests()
    {
        // Create in-memory database for testing
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(databaseName: $"TestDb_{Guid.NewGuid()}")
            .Options;

        _context = new ApplicationDbContext(options);

        // Create mocks for dependencies
        _mockDockerService = new Mock<IDockerService>();
        _mockLogger = new Mock<ILogger<ScanService>>();
        _mockBackgroundJobClient = new Mock<IBackgroundJobClient>();

        // Create ScanService with mocked dependencies
        _scanService = new ScanService(_context, _mockDockerService.Object, _mockLogger.Object, _mockBackgroundJobClient.Object);
    }

    public void Dispose()
    {
        _context.Dispose();
        GC.SuppressFinalize(this);
    }

    [Fact]
    public async Task ProcessSecurityScanAsync_WithValidReport_CompletesSuccessfully()
    {
        // Arrange
        var user = new User
        {
            Username = "testuser",
            Email = "<EMAIL>",
            PasswordHash = "hashedpassword",
            FirstName = "Test",
            LastName = "User"
        };
        _context.Users.Add(user);
        await _context.SaveChangesAsync();

        var project = new Project
        {
            Name = "Test Project",
            Description = "Test Description",
            Type = ProjectType.Static,
            UserId = user.Id,
            IsPublic = false
        };
        _context.Projects.Add(project);
        await _context.SaveChangesAsync();

        var scanReport = new ScanReport
        {
            ProjectId = project.Id,
            Type = ScanType.Security,
            Status = ScanStatus.Pending,
            CreatedAt = DateTime.UtcNow
        };
        _context.ScanReports.Add(scanReport);
        await _context.SaveChangesAsync();

        // Act
        await _scanService.ProcessSecurityScanAsync(scanReport.Id);

        // Assert
        var updatedReport = await _context.ScanReports.FindAsync(scanReport.Id);
        Assert.NotNull(updatedReport);
        Assert.Equal(ScanStatus.Completed, updatedReport.Status);
        Assert.NotNull(updatedReport.Results);
        Assert.Equal(75, updatedReport.Score);
        Assert.Contains("security", updatedReport.Summary?.ToLower() ?? "");
        Assert.NotNull(updatedReport.CompletedAt);

        // Verify results structure
        var results = JsonSerializer.Deserialize<JsonElement>(updatedReport.Results);
        Assert.True(results.TryGetProperty("vulnerabilities", out _));
        Assert.True(results.TryGetProperty("score", out _));
        Assert.True(results.TryGetProperty("recommendations", out _));
    }

    [Fact]
    public async Task ProcessSeoScanAsync_WithValidReport_CompletesSuccessfully()
    {
        // Arrange
        var user = new User
        {
            Username = "seouser",
            Email = "<EMAIL>",
            PasswordHash = "hashedpassword",
            FirstName = "SEO",
            LastName = "User"
        };
        _context.Users.Add(user);
        await _context.SaveChangesAsync();

        var project = new Project
        {
            Name = "SEO Test Project",
            Description = "SEO Test Description",
            Type = ProjectType.Static,
            UserId = user.Id,
            IsPublic = false
        };
        _context.Projects.Add(project);
        await _context.SaveChangesAsync();

        var scanReport = new ScanReport
        {
            ProjectId = project.Id,
            Type = ScanType.SEO,
            Status = ScanStatus.Pending,
            CreatedAt = DateTime.UtcNow
        };
        _context.ScanReports.Add(scanReport);
        await _context.SaveChangesAsync();

        // Act
        await _scanService.ProcessSeoScanAsync(scanReport.Id);

        // Assert
        var updatedReport = await _context.ScanReports.FindAsync(scanReport.Id);
        Assert.NotNull(updatedReport);
        Assert.Equal(ScanStatus.Completed, updatedReport.Status);
        Assert.NotNull(updatedReport.Results);
        Assert.Equal(82, updatedReport.Score);
        Assert.Contains("seo", updatedReport.Summary?.ToLower() ?? "");
        Assert.NotNull(updatedReport.CompletedAt);

        // Verify results structure
        var results = JsonSerializer.Deserialize<JsonElement>(updatedReport.Results);
        Assert.True(results.TryGetProperty("seoIssues", out _));
        Assert.True(results.TryGetProperty("score", out _));
        Assert.True(results.TryGetProperty("recommendations", out _));
    }

    [Fact]
    public async Task ProcessPerformanceScanAsync_WithValidReport_CompletesSuccessfully()
    {
        // Arrange
        var user = new User
        {
            Username = "perfuser",
            Email = "<EMAIL>",
            PasswordHash = "hashedpassword",
            FirstName = "Performance",
            LastName = "User"
        };
        _context.Users.Add(user);
        await _context.SaveChangesAsync();

        var project = new Project
        {
            Name = "Performance Test Project",
            Description = "Performance Test Description",
            Type = ProjectType.Static,
            UserId = user.Id,
            IsPublic = false
        };
        _context.Projects.Add(project);
        await _context.SaveChangesAsync();

        var scanReport = new ScanReport
        {
            ProjectId = project.Id,
            Type = ScanType.Performance,
            Status = ScanStatus.Pending,
            CreatedAt = DateTime.UtcNow
        };
        _context.ScanReports.Add(scanReport);
        await _context.SaveChangesAsync();

        // Act
        await _scanService.ProcessPerformanceScanAsync(scanReport.Id);

        // Assert
        var updatedReport = await _context.ScanReports.FindAsync(scanReport.Id);
        Assert.NotNull(updatedReport);
        Assert.Equal(ScanStatus.Completed, updatedReport.Status);
        Assert.NotNull(updatedReport.Results);
        Assert.Equal(88, updatedReport.Score);
        Assert.Contains("performance", updatedReport.Summary?.ToLower() ?? "");
        Assert.NotNull(updatedReport.CompletedAt);

        // Verify results structure
        var results = JsonSerializer.Deserialize<JsonElement>(updatedReport.Results);
        Assert.True(results.TryGetProperty("metrics", out _));
        Assert.True(results.TryGetProperty("score", out _));
        Assert.True(results.TryGetProperty("recommendations", out _));
    }

    [Fact]
    public async Task ProcessAccessibilityScanAsync_WithValidReport_CompletesSuccessfully()
    {
        // Arrange
        var user = new User
        {
            Username = "a11yuser",
            Email = "<EMAIL>",
            PasswordHash = "hashedpassword",
            FirstName = "Accessibility",
            LastName = "User"
        };
        _context.Users.Add(user);
        await _context.SaveChangesAsync();

        var project = new Project
        {
            Name = "Accessibility Test Project",
            Description = "Accessibility Test Description",
            Type = ProjectType.Static,
            UserId = user.Id,
            IsPublic = false
        };
        _context.Projects.Add(project);
        await _context.SaveChangesAsync();

        var scanReport = new ScanReport
        {
            ProjectId = project.Id,
            Type = ScanType.Accessibility,
            Status = ScanStatus.Pending,
            CreatedAt = DateTime.UtcNow
        };
        _context.ScanReports.Add(scanReport);
        await _context.SaveChangesAsync();

        // Act
        await _scanService.ProcessAccessibilityScanAsync(scanReport.Id);

        // Assert
        var updatedReport = await _context.ScanReports.FindAsync(scanReport.Id);
        Assert.NotNull(updatedReport);
        Assert.Equal(ScanStatus.Completed, updatedReport.Status);
        Assert.NotNull(updatedReport.Results);
        Assert.Equal(79, updatedReport.Score);
        Assert.Contains("accessibility", updatedReport.Summary?.ToLower() ?? "");
        Assert.NotNull(updatedReport.CompletedAt);

        // Verify results structure
        var results = JsonSerializer.Deserialize<JsonElement>(updatedReport.Results);
        Assert.True(results.TryGetProperty("accessibilityIssues", out _));
        Assert.True(results.TryGetProperty("score", out _));
        Assert.True(results.TryGetProperty("recommendations", out _));
    }

    [Fact]
    public async Task ProcessSecurityScanAsync_WithNonExistentReport_DoesNotThrow()
    {
        // Act & Assert - Should not throw exception
        await _scanService.ProcessSecurityScanAsync(99999);

        // No exception should be thrown, method should handle gracefully
        Assert.True(true); // Test passes if no exception is thrown
    }


}
