using Xunit;

namespace Siteshipserver.Tests;

public class BackgroundJobsIntegrationTests : IClassFixture<SiteshipWebApplicationFactory>
{
    private readonly SiteshipWebApplicationFactory _factory;

    public BackgroundJobsIntegrationTests(SiteshipWebApplicationFactory factory)
    {
        _factory = factory;
    }

    [Fact]
    public async Task BackgroundJobsHealthCheck_ReturnsHealthyStatus()
    {
        // Arrange
        var client = _factory.CreateClient();

        // Act
        var response = await client.GetAsync("/api/health/jobs");

        // Assert
        response.EnsureSuccessStatusCode();
        var content = await response.Content.ReadAsStringAsync();
        
        // The health check returns JSON with job status information
        Assert.Contains("isrunning", content.ToLower());
        Assert.Contains("true", content.ToLower());
    }
}
