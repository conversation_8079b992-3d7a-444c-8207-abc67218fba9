using System.Net;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using Microsoft.Extensions.DependencyInjection;
using Siteshipserver.Data;
using Xunit;

namespace Siteshipserver.Tests;

public class BasicIntegrationTests : IClassFixture<SiteshipWebApplicationFactory>
{
    private readonly SiteshipWebApplicationFactory _factory;
    private readonly HttpClient _client;

    public BasicIntegrationTests(SiteshipWebApplicationFactory factory)
    {
        _factory = factory;
        _client = _factory.CreateClient();
    }

    [Fact]
    public async Task HealthCheck_ReturnsHealthy()
    {
        // Act
        var response = await _client.GetAsync("/health");

        // Assert
        response.EnsureSuccessStatusCode();
        var content = await response.Content.ReadAsStringAsync();
        Assert.Contains("healthy", content);
    }

    [Fact]
    public async Task ApiInfo_ReturnsCorrectInfo()
    {
        // Act
        var response = await _client.GetAsync("/api/info");

        // Assert
        response.EnsureSuccessStatusCode();
        var content = await response.Content.ReadAsStringAsync();
        Assert.Contains("SiteShip.ai API", content);
        Assert.Contains("JWT Authentication", content);
    }



    [Fact]
    public async Task Database_IsAccessible()
    {
        // Arrange
        using var scope = _factory.Services.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

        // Act & Assert
        Assert.True(await context.Database.CanConnectAsync());
    }

    [Fact]
    public async Task UnauthorizedEndpoint_ReturnsUnauthorized()
    {
        // Act
        var response = await _client.GetAsync("/api/projects");

        // Assert
        Assert.Equal(HttpStatusCode.Unauthorized, response.StatusCode);
    }

    [Fact]
    public async Task NonExistentEndpoint_ReturnsNotFound()
    {
        // Act
        var response = await _client.GetAsync("/api/nonexistent");

        // Assert
        Assert.Equal(HttpStatusCode.NotFound, response.StatusCode);
    }
}
